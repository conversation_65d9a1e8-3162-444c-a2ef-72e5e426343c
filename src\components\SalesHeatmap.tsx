import React, { useState, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import QuestionDetailPopup from "./QuestionDetailPopup";

interface SalesHeatmapProps {
  categories: any[];
}

const SalesHeatmap: React.FC<SalesHeatmapProps> = ({ categories }) => {
  const [expandedCriteria, setExpandedCriteria] = useState<string | null>(null);
  const [showAllCategory, setShowAllCategory] = useState<Record<string, boolean>>({});
  const [selectedQuestion, setSelectedQuestion] = useState<any>(null);
  const [selectedSubCriteriaName, setSelectedSubCriteriaName] = useState<string>("");

  /** Color calculation */
  const getScoreColor = (actualScore: number | null, maxScore: number | null) => {
    if (actualScore === null || maxScore === null || maxScore === 0) return "bg-gray-200";
    const percentage = (actualScore / maxScore) * 100;
    if (percentage >= 90) return "bg-green-500 text-white";
    if (percentage >= 70) return "bg-yellow-400 text-gray-900";
    if (percentage >= 50) return "bg-orange-400 text-white";
    return "bg-red-500 text-white";
  };

  /** Category-level score calculation */
  const getCategoryScore = (categoryId: string) => {
    const category = categories.find((cat) => cat.id === categoryId);
    if (!category) return { actual: 0, max: 0, percentage: 0 };

    const actualTotal = category.criteria.reduce(
      (sum, criteria) => sum + (criteria.sales?.actualScore || 0),
      0
    );
    const maxTotal = category.criteria.reduce(
      (sum, criteria) => sum + (criteria.sales?.maxScore || 0),
      0
    );

    return {
      actual: Math.round(actualTotal * 100) / 100,
      max: maxTotal,
      percentage: maxTotal > 0 ? Math.round((actualTotal / maxTotal) * 100) : 0,
    };
  };

  /** Calculate total scores */
  const calculateTotalScores = (filteredCategories: any[]) => {
    let totalMaxScore = 0;
    let totalActualScore = 0;

    filteredCategories.forEach((category) => {
      category.criteria.forEach((criteria: any) => {
        totalMaxScore += criteria.sales?.maxScore || 0;
        totalActualScore += criteria.sales?.actualScore || 0;
      });
    });

    return {
      maxScore: totalMaxScore,
      actualScore: Math.round(totalActualScore * 100) / 100,
      percentage:
        totalMaxScore > 0
          ? Math.round((totalActualScore / totalMaxScore) * 10000) / 100
          : 0,
    };
  };

  const totalScores = useMemo(
    () => calculateTotalScores(categories),
    [categories]
  );

  /** Handle question click - show detailed popup */
  const handleQuestionClick = (question: any, subCriteriaName: string) => {
    setSelectedQuestion(question);
    setSelectedSubCriteriaName(subCriteriaName);
  };

  const handleQuestionPopupClose = () => {
    setSelectedQuestion(null);
    setSelectedSubCriteriaName("");
  };

  return (
    <>
      <Card className="bg-white/90 backdrop-blur-sm shadow-2xl border-0">
        <CardHeader className="bg-gradient-to-br from-blue-400 to-blue-500 text-white rounded-t-lg">
          <CardTitle className="text-2xl font-bold text-center">
            Sales Department Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-6">
            {categories.map((category) => {
              const categoryScore = getCategoryScore(category.id);

              return (
                <div key={category.id} className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-blue-100 rounded-lg">
                    <div className="flex items-center gap-3">
                      <h3 className="font-bold text-blue-900 uppercase tracking-wide">
                        {category.name}
                      </h3>
                      <button
                        onClick={() =>
                          setShowAllCategory((prev) => ({
                            ...prev,
                            [category.id]: !prev[category.id],
                          }))
                        }
                        className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                      >
                        {showAllCategory[category.id]
                          ? "Hide All Questions"
                          : "Show All Questions"}
                      </button>
                    </div>
                    <div className="text-sm font-semibold text-blue-800">
                      {categoryScore.max > 0
                        ? `${categoryScore.percentage}% (${categoryScore.actual}/${categoryScore.max})`
                        : "--"}
                    </div>
                  </div>

                  <div className="grid gap-1">
                    {category.criteria.map((criteria) => (
                      <div key={criteria.id}>
                        <div className="grid grid-cols-12 gap-1 items-center">
                          <div className="col-span-8 p-1 text-xs font-medium text-gray-700">
                            {criteria.mainCriteria}
                          </div>
                          <div className="col-span-2 text-center text-xs text-gray-500">
                            Max: {criteria.sales.maxScore || "N/A"}
                          </div>
                          <div className="col-span-2">
                            <div
                              className={`rounded w-10 h-10 flex items-center justify-center font-bold text-xs cursor-pointer ${getScoreColor(
                                criteria.sales.actualScore,
                                criteria.sales.maxScore
                              )}`}
                              onClick={() =>
                                setExpandedCriteria(
                                  expandedCriteria === criteria.id ? null : criteria.id
                                )
                              }
                            >
                              {criteria.sales.actualScore !== null
                                ? criteria.sales.actualScore
                                : "N/A"}
                            </div>
                          </div>
                        </div>

                        {/* Show subCriteria with questions inline */}
                        {(expandedCriteria === criteria.id ||
                          showAllCategory[category.id]) &&
                          criteria.subCriteriaDetails && (
                            <div className="mt-2 p-4 bg-blue-50 rounded-lg">
                              {criteria.subCriteriaDetails.map((sub) => (
                                <div key={sub.id} className="mb-3">
                                  <div className="font-semibold">{sub.name}</div>
                                  {/* Show questions inline (compact grid) */}
                                  <div className="mt-2 flex flex-wrap gap-2">
                                    {sub.questions.map((q: any) => {
                                      // Calculate percentage (same value shown in popup)
                                      const percentage =
                                        q.maxScore > 0
                                          ? Math.round((q.actualScore / q.maxScore) * 100)
                                          : 0;

                                      // Color code based on percentage (matches popup)
                                      const getBoxColor = () => {
                                        if (percentage >= 90) return "bg-green-500 text-white";
                                        if (percentage >= 70) return "bg-lime-400 text-white";
                                        if (percentage >= 50) return "bg-yellow-400 text-gray-900";
                                        if (percentage >= 30) return "bg-orange-400 text-white";
                                        return "bg-red-500 text-white";
                                      };

                                      return (
                                        <div
                                          key={q.id}
                                          className={`flex items-center justify-center rounded text-xs font-bold cursor-pointer ${getBoxColor()} w-12 h-12`}
                                          title={`${q.question} (Score: ${q.actualScore}/${q.maxScore})`}
                                          onClick={() => handleQuestionClick(q, sub.name)}
                                        >
                                          {q.id}
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}

            {/* TOTAL SALES SCORE */}
            <div className="border-t-2 border-blue-300 pt-4 mt-4">
              <div className="grid grid-cols-12 gap-2 items-center bg-gradient-to-br from-blue-400 to-blue-500 text-white rounded-lg p-3">
                <div className="col-span-8 font-bold text-lg">
                  TOTAL SALES SCORE
                </div>
                <div className="col-span-2 text-center font-semibold">
                  {totalScores.maxScore}
                </div>
                <div className="col-span-2 text-center">
                  <div className="bg-white text-blue-700 rounded-lg p-2 font-bold text-lg">
                    {totalScores.actualScore}
                  </div>
                </div>
              </div>
              <div className="mt-2 text-center">
                <div className="text-sm text-gray-600">
                  Percentage:{" "}
                  <span className="font-bold text-blue-600">
                    {totalScores.percentage}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Question Popup */}
      <QuestionDetailPopup
        isOpen={!!selectedQuestion}
        onClose={handleQuestionPopupClose}
        question={selectedQuestion}
        subCriteriaName={selectedSubCriteriaName}
      />
    </>
  );
};

export default SalesHeatmap;
