import React, { useState, useEffect } from 'react';
import { transformPreApiData } from '../utils/dataTransform';
import SalesHeatmap from './SalesHeatmap';
import ServiceHeatmap from './ServiceHeatmap';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DealerFilter from './DealerFilter';

// API fetch function
async function fetchPreApiData() {
  const API_ENDPOINT = 'https://api.eisqr.com/dealer-checklist-submissions/latest-by-vendor';
  const response = await fetch(API_ENDPOINT);
  if (!response.ok) throw new Error('API fetch failed');

  const data = await response.json();
  return data.map((item: any) => ({
    ...item,
    response: typeof item.response === 'string' ? JSON.parse(item.response) : item.response,
    score: typeof item.score === 'string' ? JSON.parse(item.score) : item.score,
  }));
}

// Custom hook for data fetching
function useMsiData() {
  const [rawData, setRawData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const apiData = await fetchPreApiData();
        setRawData(apiData);
        setError(null);
      } catch (err) {
        console.error('MSI data load error:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  return { rawData, loading, error };
}

const MSIHeatmap = () => {
  const { rawData, loading, error } = useMsiData();

  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [transformedData, setTransformedData] = useState<any>(null);

  // When rawData changes, set default filteredData
  useEffect(() => {
    if (rawData.length > 0) {
      setFilteredData(rawData);
    }
  }, [rawData]);

  // Transform whenever filteredData changes
  useEffect(() => {
    const transform = async () => {
      if (filteredData.length > 0) {
        const transformed = await transformPreApiData(filteredData);
        setTransformedData(transformed);
      } else {
        setTransformedData(null);
      }
    };
    transform();
  }, [filteredData]);

  // Loading state
  if (loading) {
    return (
      <FullScreenMessage
        icon={<div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>}
        text="Loading heatmap data..."
      />
    );
  }

  // Error state
  if (error || !transformedData) {
    return (
      <FullScreenMessage
        icon={<div className="text-red-500 text-6xl mb-4">⚠️</div>}
        text={error || 'Failed to load data'}
        action={() => window.location.reload()}
      />
    );
  }

  const { totals = {}, categories = [] } = transformedData;

  // Safely calculate dealership score if not present
  const dealershipScore =
    totals.dealershipScore ??
    Math.round(
      ((totals.sales?.percentage || 0) + (totals.service?.percentage || 0)) / 2
    );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8 overflow-visible">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Dealer MSI Calibration Score
          </h1>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <SummaryCard
            title="Sales Score"
            percentage={totals.sales?.percentage}
            actual={totals.sales?.actualScore}
            max={totals.sales?.maxScore}
            gradient="from-blue-400 to-blue-500"
          />
          <SummaryCard
            title="Service Score"
            percentage={totals.service?.percentage}
            actual={totals.service?.actualScore}
            max={totals.service?.maxScore}
            gradient="from-purple-400 to-purple-500"
          />
          <SummaryCard
            title="Overall Dealership Score"
            percentage={dealershipScore}
            subtext="Combined Average"
            gradient="from-sky-300 to-sky-400"
          />
        </div>

        {/* Filter */}
        <DealerFilter rawData={rawData} onFilter={setFilteredData} />

        {/* Heatmaps */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <SalesHeatmap categories={categories} />
          <ServiceHeatmap categories={categories} />
        </div>
      </div>
    </div>
  );
};

export default MSIHeatmap;

/* Reusable Components */
const FullScreenMessage = ({ icon, text, action }: any) => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
    <div className="text-center">
      {icon}
      <p className="mt-4 text-lg text-gray-600">{text}</p>
      {action && (
        <button
          onClick={action}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Retry
        </button>
      )}
    </div>
  </div>
);

const SummaryCard = ({ title, percentage, actual, max, subtext, gradient }: any) => (
  <Card className={`bg-gradient-to-br ${gradient} text-white shadow-xl`}>
    <CardHeader className="pb-2">
      <CardTitle className="text-lg font-semibold">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="text-3xl font-bold">{percentage ?? 'N/A'}%</div>
      <div className="text-blue-100 text-sm">
        {subtext || `${actual ?? '-'} / ${max ?? '-'}`}
      </div>
    </CardContent>
  </Card>
);
