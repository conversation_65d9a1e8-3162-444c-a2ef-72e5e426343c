import React, { useState, useMemo } from "react";
import { zonalOfficeList, categoryList } from "./constants";

interface DealerFilterProps {
    rawData: any[];
    onFilter: (filteredData: any[]) => void;
}

const DealerFilter: React.FC<DealerFilterProps> = ({ rawData, onFilter }) => {
    const [filters, setFilters] = useState({
        zone: "",
        city: "",
        dealer: "",
        category: "",
        ao: "",
        from: "",
        to: "",
    });

    // Build dropdown options dynamically with cascading logic
    const options = useMemo(() => {
        const zones = new Set<number>();
        const cities = new Set<string>();
        const dealers = new Set<string>();
        const aos = new Set<string>();
        const categories = new Set<number>();

        rawData.forEach((item) => {
            const vendor = item.vendor || {};
            // Apply cascading filters
            if (filters.zone && vendor.dealerZone !== parseInt(filters.zone)) return;
            if (filters.city && vendor.dealerLocation !== filters.city) return;
            if (
                filters.dealer &&
                `${vendor.dealerName} (${vendor.code})` !== filters.dealer
            )
                return;
            if (filters.ao && vendor.dealerAO !== filters.ao) return;

            // Populate sets
            if (vendor.dealerZone) zones.add(vendor.dealerZone);
            if (vendor.dealerLocation) cities.add(vendor.dealerLocation);
            if (vendor.dealerName)
                dealers.add(`${vendor.dealerName} (${vendor.code})`);
            if (vendor.dealerAO) aos.add(vendor.dealerAO);
            if (vendor.dealerCategory) categories.add(vendor.dealerCategory);
        });

        return {
            zones: zonalOfficeList.filter((z) => zones.has(z.value)),
            cities: Array.from(cities),
            dealers: Array.from(dealers),
            aos: Array.from(aos),
            categories: categoryList.filter((c) => categories.has(c.value)),
        };
    }, [rawData, filters.zone, filters.city, filters.dealer, filters.ao]);

    // Handle filter change
    const handleChange = (key: string, value: string) => {
        const updated = { ...filters, [key]: value };

        // Reset dependent filters
        if (key === "zone") {
            updated.city = "";
            updated.dealer = "";
            updated.ao = "";
        }
        if (key === "city") {
            updated.dealer = "";
            updated.ao = "";
        }
        if (key === "dealer") {
            updated.ao = "";
        }

        setFilters(updated);

        // Apply filtering on rawData
        const filtered = rawData.filter((item) => {
            const vendor = item.vendor || {};
            const createdDate = new Date(item.created_on);

            return (
                (!updated.zone || vendor.dealerZone === parseInt(updated.zone)) &&
                (!updated.city || vendor.dealerLocation === updated.city) &&
                (!updated.dealer ||
                    `${vendor.dealerName} (${vendor.code})` === updated.dealer) &&
                (!updated.category ||
                    vendor.dealerCategory === parseInt(updated.category)) &&
                (!updated.ao || vendor.dealerAO === updated.ao) &&
                (!updated.from ||
                    createdDate >= new Date(updated.from)) &&
                (!updated.to || createdDate <= new Date(updated.to))
            );
        });

        onFilter(filtered);
    };

    return (
        <div className="bg-gray-50 p-4 rounded-lg shadow">
            {/* First row: Zone to AO */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-4">
                {/* Zone */}
                <div>
                    <label className="block text-sm font-semibold mb-1">Zone</label>
                    <select
                        className="border rounded p-2 w-full"
                        value={filters.zone}
                        onChange={(e) => handleChange("zone", e.target.value)}
                    >
                        <option value="">All</option>
                        {options.zones.map((z) => (
                            <option key={z.value} value={z.value}>
                                {z.name}
                            </option>
                        ))}
                    </select>
                </div>

                {/* City */}
                <div>
                    <label className="block text-sm font-semibold mb-1">City</label>
                    <select
                        className="border rounded p-2 w-full"
                        value={filters.city}
                        onChange={(e) => handleChange("city", e.target.value)}
                    >
                        <option value="">All</option>
                        {options.cities.map((c) => (
                            <option key={c} value={c}>
                                {c}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Dealer */}
                <div>
                    <label className="block text-sm font-semibold mb-1">
                        Dealer (Name & Code)
                    </label>
                    <select
                        className="border rounded p-2 w-full"
                        value={filters.dealer}
                        onChange={(e) => handleChange("dealer", e.target.value)}
                    >
                        <option value="">All</option>
                        {options.dealers.map((d) => (
                            <option key={d} value={d}>
                                {d}
                            </option>
                        ))}
                    </select>
                </div>

                {/* Category */}
                <div>
                    <label className="block text-sm font-semibold mb-1">Category</label>
                    <select
                        className="border rounded p-2 w-full"
                        value={filters.category}
                        onChange={(e) => handleChange("category", e.target.value)}
                    >
                        <option value="">All</option>
                        {options.categories.map((cat) => (
                            <option key={cat.value} value={cat.value}>
                                {cat.name}
                            </option>
                        ))}
                    </select>
                </div>

                {/* AO */}
                <div>
                    <label className="block text-sm font-semibold mb-1">Area Office</label>
                    <select
                        className="border rounded p-2 w-full"
                        value={filters.ao}
                        onChange={(e) => handleChange("ao", e.target.value)}
                    >
                        <option value="">All</option>
                        {options.aos.map((a) => (
                            <option key={a} value={a}>
                                {a}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Second row: Date range */}
            {/* Second row: Date range */}
            <div className="flex gap-4">
                <div className="max-w-[200px]">
                    <label className="block text-sm font-semibold mb-1">From Date</label>
                    <input
                        type="date"
                        className="border rounded p-2 w-full"
                        value={filters.from}
                        onChange={(e) => handleChange("from", e.target.value)}
                    />
                </div>

                <div className="max-w-[200px]">
                    <label className="block text-sm font-semibold mb-1">To Date</label>
                    <input
                        type="date"
                        className="border rounded p-2 w-full"
                        value={filters.to}
                        onChange={(e) => handleChange("to", e.target.value)}
                    />
                </div>
            </div>


        </div>
    );
};

export default DealerFilter;
